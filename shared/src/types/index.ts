import type { Tables, TablesInsert } from "./database";

export type ApiResponse = {
  message: string;
  success: true;
};

// Database entity types - use generated types from database.ts
export type Receipt = Tables<"receipts">;
export type ReceiptItem = Tables<"receipt_items">;
export type MasterItem = Tables<"master_items">;
export type ItemMatch = Tables<"item_matches">;

// Input types for creating new records - use generated insert types
export type CreateReceiptInput = TablesInsert<"receipts">;
export type CreateReceiptItemInput = TablesInsert<"receipt_items">;

// Extended types with related data
export interface ReceiptWithItems extends Receipt {
  items: ReceiptItem[];
}

export interface ReceiptItemWithMasterItem extends ReceiptItem {
  master_item?: MasterItem;
  item_match?: ItemMatch;
}

// Parsed receipt data from PDF processing - derived from database types
export interface ParsedReceiptData extends Omit<CreateReceiptInput, "user_id"> {
  // Line items - derived from database types
  items: Array<Omit<CreateReceiptItemInput, "receipt_id">>;

  // Raw text for debugging (required for parsed data)
  raw_pdf_text: string;
}

// Analytics response types
export interface MonthlySpendingData {
  month: string; // YYYY-MM format
  total_spent: number;
  receipt_count: number;
  average_per_receipt: number;
}

export interface ItemPriceHistory {
  item_name: string;
  price_points: Array<{
    date: string; // ISO date
    price: number;
    store_name: string;
    receipt_id: string;
  }>;
  average_price: number;
  min_price: number;
  max_price: number;
  price_trend: "increasing" | "decreasing" | "stable";
}

export interface StoreSpendingData {
  store_name: string;
  total_spent: number;
  receipt_count: number;
  average_per_receipt: number;
  first_visit: string; // ISO date
  last_visit: string; // ISO date
}

export interface ExpensiveItemData {
  item_name: string;
  highest_price: number;
  average_price: number;
  purchase_count: number;
  total_spent: number;
  stores: string[]; // List of stores where this item was purchased
}

// API Response types
export interface ReceiptProcessingResponse {
  success: boolean;
  receipt?: Receipt;
  items?: ReceiptItem[];
  parsing_errors?: string[];
  message: string;
}

export interface AnalyticsResponse<T> {
  success: boolean;
  data: T;
  period?: {
    start_date: string;
    end_date: string;
  };
  message: string;
}

// Query parameter types for analytics endpoints
export interface AnalyticsQueryParams {
  start_date?: string; // ISO date
  end_date?: string; // ISO date
  store_name?: string;
  user_id?: string;
}

export interface ItemPriceQueryParams extends AnalyticsQueryParams {
  item_name: string;
}

// Error response type
export interface ErrorResponse {
  success: false;
  error: string;
  details?: any;
}
