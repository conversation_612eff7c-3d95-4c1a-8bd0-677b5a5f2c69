import { <PERSON>o } from "hono";
import { cors } from "hono/cors";
import type { ApiResponse, ReceiptProcessingResponse } from "shared/dist";
// @ts-ignore - pdf-extraction doesn't have types
import extract from "pdf-extraction";
import { ReceiptParser } from "./services/receiptParser";
import { ReceiptService } from "./services/receiptService";
import { AnalyticsService } from "./services/analyticsService";
import { UserService } from "./services/userService";
import { supabaseMiddleware } from "./lib/middleware/supabase";
import {
  supabaseServiceRoleMiddleware,
  serviceRoleOperations,
} from "./lib/supabaseServiceRole";

// Detailed logging utility - deployment-friendly
const logWithTimestamp = (
  level: "INFO" | "ERROR" | "WARN" | "DEBUG",
  message: string,
  data?: any
) => {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    level,
    message,
    ...(data && { data }),
  };

  // JSON format for structured logging
  const jsonLog = JSON.stringify(logEntry);

  // Plain text format for better compatibility
  const plainLog = `[${timestamp}] ${level}: ${message}${data ? ` | ${JSON.stringify(data)}` : ""}`;

  // Output to multiple streams for better compatibility
  if (level === "ERROR") {
    // Errors to stderr (more likely to be captured)
    console.error(jsonLog);
    console.error(plainLog);
    process.stderr.write(jsonLog + "\n");
  } else {
    // Regular logs to stdout
    console.log(jsonLog);
    console.log(plainLog);
    process.stdout.write(jsonLog + "\n");
  }

  // Force flush for immediate output
  try {
    (process.stdout as any).flush?.();
    (process.stderr as any).flush?.();
  } catch (e) {
    // Ignore flush errors
  }
};

// Log startup immediately
logWithTimestamp("INFO", "🚀 Server starting up", {
  nodeVersion: process.version,
  platform: process.platform,
  env: process.env.NODE_ENV || "development",
  port: process.env.PORT || 3000,
});

const app = new Hono();
app.use("*", supabaseMiddleware());
app.use("*", supabaseServiceRoleMiddleware());
app.use(cors());

// Add request logging middleware
app.use("*", async (c, next) => {
  const start = Date.now();
  const method = c.req.method;
  const path = c.req.path;

  logWithTimestamp("INFO", "Incoming request", {
    method,
    path,
    userAgent: c.req.header("user-agent"),
    contentLength: c.req.header("content-length"),
    contentType: c.req.header("content-type"),
  });

  await next();

  const duration = Date.now() - start;
  logWithTimestamp("INFO", "Request completed", {
    method,
    path,
    duration,
    status: c.res.status,
  });
});

app.get("/", (c) => {
  logWithTimestamp("INFO", "🏠 Root endpoint accessed");
  return c.text("Hello Hono!");
});

// Simple test endpoint for debugging logs
app.get("/test-logs", (c) => {
  logWithTimestamp("INFO", "📝 Test logs endpoint called");
  logWithTimestamp("WARN", "⚠️ This is a warning log");
  logWithTimestamp("ERROR", "❌ This is an error log");
  logWithTimestamp("DEBUG", "🐛 This is a debug log");

  return c.json({
    message: "Logs have been written - check your deployment logs",
    timestamp: new Date().toISOString(),
    logLevels: ["INFO", "WARN", "ERROR", "DEBUG"],
  });
});

app.get("/hello", async (c) => {
  const data: ApiResponse = {
    message: "Hello BHVR!",
    success: true,
  };

  return c.json(data, { status: 200 });
});

// Health check endpoint for debugging
app.get("/health", async (c) => {
  const health = {
    status: "healthy",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: "1.0.0",
    endpoints: {
      "/": "Basic hello endpoint",
      "/hello": "API test endpoint",
      "/handle-ticket-forwarding": "Mailgun webhook handler",
      "/health": "Health check endpoint",
    },
  };

  logWithTimestamp("INFO", "Health check requested", health);
  return c.json(health, { status: 200 });
});

// Types for email processing
interface EmailData {
  recipient: string;
  sender: string;
  from: string;
  to: string;
  subject: string;
  bodyPlain: string;
  bodyHtml: string;
  attachmentCount: number;
  timestamp: string;
  messageHeaders: string;
}

interface PdfAttachment {
  filename: string;
  size: number;
  buffer: Buffer;
}

// Extract email data from Mailgun webhook payload
const extractEmailData = (body: any): EmailData => {
  logWithTimestamp("DEBUG", "Extracting email data from body", {
    availableKeys: Object.keys(body).slice(0, 20), // Log first 20 keys to avoid huge logs
  });

  const emailData = {
    recipient: body.recipient || body.to || "unknown",
    sender: body.sender || body.from || "unknown",
    from: body.from || "unknown",
    to: body.to || body.recipient || "unknown",
    subject: body.subject || "No subject",
    bodyPlain: body["body-plain"] || body.bodyPlain || "",
    bodyHtml: body["body-html"] || body.bodyHtml || "",
    attachmentCount: parseInt(
      body["attachment-count"] || body.attachmentCount || "0"
    ),
    timestamp: body.timestamp || new Date().toISOString(),
    messageHeaders: body["message-headers"] || body.messageHeaders || "",
  };

  logWithTimestamp("DEBUG", "Extracted email data", emailData);
  return emailData;
};

// Extract PDF attachments from Mailgun webhook payload
const extractPdfAttachments = async (body: any): Promise<PdfAttachment[]> => {
  const attachments: PdfAttachment[] = [];

  logWithTimestamp("DEBUG", "Looking for PDF attachments", {
    attachmentCount: body["attachment-count"] || body.attachmentCount || 0,
    bodyKeys: Object.keys(body)
      .filter((key) => key.includes("attachment"))
      .slice(0, 10),
  });

  // Mailgun sends attachments as attachment-1, attachment-2, etc.
  const attachmentCount = parseInt(
    body["attachment-count"] || body.attachmentCount || "0"
  );

  for (let i = 1; i <= attachmentCount; i++) {
    const attachmentKey = `attachment-${i}`;
    const attachment = body[attachmentKey];

    if (attachment) {
      logWithTimestamp("DEBUG", `Found attachment ${i}`, {
        attachmentKey,
        hasAttachment: !!attachment,
        attachmentType: typeof attachment,
      });

      // Check if it's a PDF
      let filename = "";
      let buffer: Buffer | null = null;

      if (attachment instanceof File) {
        filename = attachment.name;
        if (filename.toLowerCase().endsWith(".pdf")) {
          const arrayBuffer = await attachment.arrayBuffer();
          buffer = Buffer.from(arrayBuffer);
        }
      } else if (typeof attachment === "object" && attachment.filename) {
        filename = attachment.filename;
        if (filename.toLowerCase().endsWith(".pdf") && attachment.content) {
          buffer = Buffer.from(attachment.content, "base64");
        }
      } else if (typeof attachment === "string") {
        // Sometimes attachments come as base64 strings
        const filenameKey = `attachment-${i}-filename`;
        filename = body[filenameKey] || `attachment-${i}.pdf`;
        if (filename.toLowerCase().endsWith(".pdf")) {
          buffer = Buffer.from(attachment, "base64");
        }
      }

      if (buffer && filename.toLowerCase().endsWith(".pdf")) {
        attachments.push({
          filename,
          size: buffer.length,
          buffer,
        });
        logWithTimestamp("INFO", `Added PDF attachment ${i}`, {
          filename,
          size: buffer.length,
        });
      } else {
        logWithTimestamp("DEBUG", `Skipped non-PDF attachment ${i}`, {
          filename,
          isPdf: filename.toLowerCase().endsWith(".pdf"),
          hasBuffer: !!buffer,
        });
      }
    }
  }

  return attachments;
};

// Parse PDF content using pdf-extraction
const parsePdfContent = async (buffer: Buffer): Promise<string> => {
  logWithTimestamp("DEBUG", "Starting PDF parsing", {
    bufferSize: buffer.length,
  });

  try {
    // Use pdf-extraction to extract text from the PDF buffer
    const data = await extract(buffer);

    logWithTimestamp("DEBUG", "PDF parsing completed", {
      textLength: data.text.length,
      numPages: data.pages?.length || 0,
      metadata: data.meta || {},
    });

    // Log the full PDF content for parser development
    logWithTimestamp("INFO", "📄 PDF Content Extracted", {
      filename: "PDF_CONTENT_FOR_PARSER",
      contentLength: data.text.length,
      content: data.text,
    });

    return data.text;
  } catch (error) {
    logWithTimestamp("ERROR", "PDF parsing failed", {
      error: error instanceof Error ? error.message : String(error),
      bufferSize: buffer.length,
    });
    throw error;
  }
};

app.post("/handle-ticket-forwarding", async (c) => {
  const startTime = Date.now();
  logWithTimestamp("INFO", "Received ticket forwarding request", {
    headers: c.req.header(),
    contentType: c.req.header("content-type"),
    method: c.req.method,
    url: c.req.url,
  });

  try {
    // Parse the request body based on content type
    let body: any;
    const contentType = c.req.header("content-type") || "";

    logWithTimestamp("DEBUG", "Parsing request body", { contentType });

    if (contentType.includes("application/json")) {
      body = await c.req.json();
      logWithTimestamp("DEBUG", "Parsed JSON body", {
        bodyKeys: Object.keys(body),
      });
    } else if (
      contentType.includes("application/x-www-form-urlencoded") ||
      contentType.includes("multipart/form-data")
    ) {
      // Mailgun typically sends form data
      body = await c.req.parseBody();
      logWithTimestamp("DEBUG", "Parsed form body", {
        bodyKeys: Object.keys(body),
      });
    } else {
      const text = await c.req.text();
      logWithTimestamp("DEBUG", "Parsed text body", {
        textLength: text.length,
      });
      body = { rawText: text };
    }

    // Extract email information
    const emailData = extractEmailData(body);
    logWithTimestamp("INFO", "Extracted email data", emailData);

    // Step 2: Look up user by email alias
    const userService = new UserService();
    let targetUserId: string | null = null;
    let userLookupError: string | null = null;

    try {
      // Extract email alias from recipient (e.g., "<EMAIL>" -> "happy-beaver-123")
      const emailAlias = emailData.recipient.split("@")[0];
      if (!emailAlias) {
        throw new Error("Could not extract email alias from recipient");
      }

      logWithTimestamp("INFO", "Looking up user by email alias", {
        emailAlias,
      });

      const user = await userService.getUserByEmailAlias(emailAlias);
      if (user) {
        targetUserId = user.user_id;
        logWithTimestamp("INFO", "Found user for email alias", {
          emailAlias,
          userId: targetUserId,
        });
      } else {
        userLookupError = `No user found for email alias: ${emailAlias}`;
        logWithTimestamp("WARN", "No user found for email alias", {
          emailAlias,
        });
      }
    } catch (error) {
      userLookupError = `Failed to lookup user: ${error instanceof Error ? error.message : String(error)}`;
      logWithTimestamp("ERROR", "User lookup failed", {
        error: userLookupError,
        recipient: emailData.recipient,
      });
    }

    // Check for PDF attachments
    const pdfAttachments = await extractPdfAttachments(body);
    logWithTimestamp("INFO", "Found PDF attachments", {
      count: pdfAttachments.length,
    });

    // Process each PDF attachment
    const pdfResults = [];
    const receiptService = new ReceiptService();

    for (let i = 0; i < pdfAttachments.length; i++) {
      const attachment = pdfAttachments[i];
      if (!attachment) {
        logWithTimestamp("WARN", `Attachment ${i + 1} is undefined, skipping`);
        continue;
      }

      logWithTimestamp("INFO", `Processing PDF attachment ${i + 1}`, {
        filename: attachment.filename,
        size: attachment.size,
      });

      try {
        const pdfContent = await parsePdfContent(attachment.buffer);

        // Parse receipt data from PDF content
        const parsedReceipt = ReceiptParser.parseReceiptText(pdfContent);

        logWithTimestamp("INFO", `Parsed receipt data from PDF ${i + 1}`, {
          filename: attachment.filename,
          storeName: parsedReceipt.store_name,
          purchaseDate: parsedReceipt.purchase_date,
          purchaseTotal: parsedReceipt.purchase_total,
          itemCount: parsedReceipt.items.length,
        });

        // Store receipt in database
        let storedReceipt = null;
        let storedItems = null;
        let dbError = null;

        try {
          if (targetUserId) {
            // Use service role operations to create receipt for specific user (bypasses RLS)
            const receipt = await serviceRoleOperations.insertReceiptForUser(
              targetUserId,
              {
                store_name: parsedReceipt.store_name,
                store_address: parsedReceipt.store_address,
                store_city: parsedReceipt.store_city,
                store_state: parsedReceipt.store_state,
                store_zip_code: parsedReceipt.store_zip_code,
                store_phone_number: parsedReceipt.store_phone_number,
                purchase_date: parsedReceipt.purchase_date,
                purchase_time: parsedReceipt.purchase_time,
                ticket_number: parsedReceipt.ticket_number,
                purchase_total: parsedReceipt.purchase_total,
                purchase_tax: parsedReceipt.purchase_tax,
                credit_card_last_4: parsedReceipt.credit_card_last_4,
                payment_method: parsedReceipt.payment_method,
                raw_pdf_text: pdfContent,
              }
            );

            // Create receipt items
            const itemsToInsert = parsedReceipt.items.map((item, index) => ({
              receipt_id: receipt.id,
              item_name: item.item_name,
              item_price: item.item_price,
              item_quantity: item.item_quantity,
              item_total_price: item.item_total_price,
              line_order: item.line_order || index + 1,
            }));

            const items =
              await serviceRoleOperations.insertReceiptItems(itemsToInsert);

            storedReceipt = receipt;
            storedItems = items;
          } else {
            // Fallback: create receipt without user (will have null user_id)
            const result = await receiptService.createReceiptWithItems(
              c,
              {
                user_id: null,
                store_name: parsedReceipt.store_name,
                store_address: parsedReceipt.store_address,
                store_city: parsedReceipt.store_city,
                store_state: parsedReceipt.store_state,
                store_zip_code: parsedReceipt.store_zip_code,
                store_phone_number: parsedReceipt.store_phone_number,
                purchase_date: parsedReceipt.purchase_date,
                purchase_time: parsedReceipt.purchase_time,
                ticket_number: parsedReceipt.ticket_number,
                purchase_total: parsedReceipt.purchase_total,
                purchase_tax: parsedReceipt.purchase_tax,
                credit_card_last_4: parsedReceipt.credit_card_last_4,
                payment_method: parsedReceipt.payment_method,
                raw_pdf_text: pdfContent,
              },
              parsedReceipt.items
            );
            storedReceipt = result.receipt;
            storedItems = result.items;
          }

          logWithTimestamp(
            "INFO",
            `Successfully stored receipt ${i + 1} in database`,
            {
              receiptId: storedReceipt.id,
              itemCount: storedItems.length,
            }
          );
        } catch (dbError_) {
          dbError =
            dbError_ instanceof Error ? dbError_.message : String(dbError_);
          logWithTimestamp(
            "ERROR",
            `Failed to store receipt ${i + 1} in database`,
            {
              filename: attachment.filename,
              error: dbError,
            }
          );
        }

        pdfResults.push({
          filename: attachment.filename,
          size: attachment.size,
          content: pdfContent,
          success: true,
          parsed_receipt: parsedReceipt,
          stored_receipt: storedReceipt,
          stored_items: storedItems,
          db_error: dbError,
        });

        logWithTimestamp("INFO", `Successfully processed PDF ${i + 1}`, {
          filename: attachment.filename,
          contentLength: pdfContent.length,
          storedInDb: !dbError,
        });
      } catch (pdfError) {
        logWithTimestamp("ERROR", `Failed to parse PDF ${i + 1}`, {
          filename: attachment.filename,
          error:
            pdfError instanceof Error ? pdfError.message : String(pdfError),
        });
        pdfResults.push({
          filename: attachment.filename,
          size: attachment.size,
          content: null,
          success: false,
          error:
            pdfError instanceof Error ? pdfError.message : String(pdfError),
        });
      }
    }

    const processingTime = Date.now() - startTime;
    const response = {
      success: true,
      processingTimeMs: processingTime,
      email: emailData,
      userLookup: {
        targetUserId,
        error: userLookupError,
        emailAlias: emailData.recipient.split("@")[0],
      },
      pdfAttachments: pdfResults,
      totalAttachments: pdfAttachments.length,
    };

    logWithTimestamp("INFO", "Successfully processed ticket forwarding", {
      processingTimeMs: processingTime,
      emailRecipient: emailData.recipient,
      emailSender: emailData.sender,
      pdfCount: pdfResults.length,
      successfulPdfs: pdfResults.filter((p) => p.success).length,
    });

    return c.json(response, { status: 200 });
  } catch (error) {
    const processingTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorStack = error instanceof Error ? error.stack : undefined;

    logWithTimestamp("ERROR", "Failed to process ticket forwarding", {
      error: errorMessage,
      stack: errorStack,
      processingTimeMs: processingTime,
    });

    return c.json(
      {
        success: false,
        error: errorMessage,
        processingTimeMs: processingTime,
      },
      { status: 500 }
    );
  }
});

// Receipt management endpoints
app.get("/receipts", async (c) => {
  try {
    const userId = c.req.query("user_id"); // For now, get from query param
    const limit = parseInt(c.req.query("limit") || "50");
    const offset = parseInt(c.req.query("offset") || "0");

    const receiptService = new ReceiptService();
    const receipts = await receiptService.getUserReceipts(
      c,
      userId,
      limit,
      offset
    );

    logWithTimestamp("INFO", "Retrieved user receipts", {
      userId,
      count: receipts.length,
      limit,
      offset,
    });

    return c.json({
      success: true,
      data: receipts,
      count: receipts.length,
      limit,
      offset,
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logWithTimestamp("ERROR", "Failed to retrieve receipts", {
      error: errorMessage,
    });

    return c.json(
      {
        success: false,
        error: errorMessage,
      },
      { status: 500 }
    );
  }
});

app.get("/receipts/:id", async (c) => {
  try {
    const receiptId = c.req.param("id");

    const receiptService = new ReceiptService();
    const receipt = await receiptService.getReceiptWithItems(c, receiptId);

    if (!receipt) {
      return c.json(
        {
          success: false,
          error: "Receipt not found",
        },
        { status: 404 }
      );
    }

    logWithTimestamp("INFO", "Retrieved receipt details", {
      receiptId,
      storeName: receipt.store_name,
      itemCount: receipt.items?.length || 0,
    });

    return c.json({
      success: true,
      data: receipt,
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logWithTimestamp("ERROR", "Failed to retrieve receipt", {
      error: errorMessage,
      receiptId: c.req.param("id"),
    });

    return c.json(
      {
        success: false,
        error: errorMessage,
      },
      { status: 500 }
    );
  }
});

app.delete("/receipts/:id", async (c) => {
  try {
    const receiptId = c.req.param("id");
    const userId = c.req.query("user_id"); // For now, get from query param

    const receiptService = new ReceiptService();
    const deleted = await receiptService.deleteReceipt(c, receiptId, userId);

    if (!deleted) {
      return c.json(
        {
          success: false,
          error: "Receipt not found or could not be deleted",
        },
        { status: 404 }
      );
    }

    logWithTimestamp("INFO", "Deleted receipt", {
      receiptId,
      userId,
    });

    return c.json({
      success: true,
      message: "Receipt deleted successfully",
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logWithTimestamp("ERROR", "Failed to delete receipt", {
      error: errorMessage,
      receiptId: c.req.param("id"),
    });

    return c.json(
      {
        success: false,
        error: errorMessage,
      },
      { status: 500 }
    );
  }
});

// Analytics endpoints
app.get("/analytics/monthly-spending", async (c) => {
  try {
    const userId = c.req.query("user_id");
    const startDate = c.req.query("start_date");
    const endDate = c.req.query("end_date");
    const storeName = c.req.query("store_name");

    const analyticsService = new AnalyticsService();
    const monthlyData = await analyticsService.getMonthlySpending(c, {
      user_id: userId,
      start_date: startDate,
      end_date: endDate,
      store_name: storeName,
    });

    logWithTimestamp("INFO", "Retrieved monthly spending analytics", {
      userId,
      dataPoints: monthlyData.length,
      startDate,
      endDate,
      storeName,
    });

    return c.json({
      success: true,
      data: monthlyData,
      period: {
        start_date: startDate || "all time",
        end_date: endDate || "all time",
      },
      message: "Monthly spending data retrieved successfully",
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logWithTimestamp("ERROR", "Failed to retrieve monthly spending", {
      error: errorMessage,
    });

    return c.json(
      {
        success: false,
        error: errorMessage,
      },
      { status: 500 }
    );
  }
});

app.get("/analytics/item-prices", async (c) => {
  try {
    const itemName = c.req.query("item_name");
    if (!itemName) {
      return c.json(
        {
          success: false,
          error: "item_name parameter is required",
        },
        { status: 400 }
      );
    }

    const userId = c.req.query("user_id");
    const startDate = c.req.query("start_date");
    const endDate = c.req.query("end_date");
    const storeName = c.req.query("store_name");

    const analyticsService = new AnalyticsService();
    const priceHistory = await analyticsService.getItemPriceHistory(c, {
      item_name: itemName,
      user_id: userId,
      start_date: startDate,
      end_date: endDate,
      store_name: storeName,
    });

    if (!priceHistory) {
      return c.json(
        {
          success: false,
          error: "No price data found for the specified item",
        },
        { status: 404 }
      );
    }

    logWithTimestamp("INFO", "Retrieved item price history", {
      itemName,
      userId,
      pricePoints: priceHistory.price_points.length,
      averagePrice: priceHistory.average_price,
      priceTrend: priceHistory.price_trend,
    });

    return c.json({
      success: true,
      data: priceHistory,
      period: {
        start_date: startDate || "all time",
        end_date: endDate || "all time",
      },
      message: "Item price history retrieved successfully",
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logWithTimestamp("ERROR", "Failed to retrieve item price history", {
      error: errorMessage,
    });

    return c.json(
      {
        success: false,
        error: errorMessage,
      },
      { status: 500 }
    );
  }
});

app.get("/analytics/store-spending", async (c) => {
  try {
    const userId = c.req.query("user_id");
    const startDate = c.req.query("start_date");
    const endDate = c.req.query("end_date");
    const storeName = c.req.query("store_name");

    const analyticsService = new AnalyticsService();
    const storeData = await analyticsService.getStoreSpending(c, {
      user_id: userId,
      start_date: startDate,
      end_date: endDate,
      store_name: storeName,
    });

    logWithTimestamp("INFO", "Retrieved store spending analytics", {
      userId,
      storeCount: storeData.length,
      startDate,
      endDate,
      storeName,
    });

    return c.json({
      success: true,
      data: storeData,
      period: {
        start_date: startDate || "all time",
        end_date: endDate || "all time",
      },
      message: "Store spending data retrieved successfully",
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logWithTimestamp("ERROR", "Failed to retrieve store spending", {
      error: errorMessage,
    });

    return c.json(
      {
        success: false,
        error: errorMessage,
      },
      { status: 500 }
    );
  }
});

app.get("/analytics/expensive-items", async (c) => {
  try {
    const userId = c.req.query("user_id");
    const startDate = c.req.query("start_date");
    const endDate = c.req.query("end_date");
    const storeName = c.req.query("store_name");
    const limit = parseInt(c.req.query("limit") || "20");

    const analyticsService = new AnalyticsService();
    const expensiveItems = await analyticsService.getExpensiveItems(
      c,
      {
        user_id: userId,
        start_date: startDate,
        end_date: endDate,
        store_name: storeName,
      },
      limit
    );

    logWithTimestamp("INFO", "Retrieved expensive items analytics", {
      userId,
      itemCount: expensiveItems.length,
      limit,
      startDate,
      endDate,
      storeName,
    });

    return c.json({
      success: true,
      data: expensiveItems,
      period: {
        start_date: startDate || "all time",
        end_date: endDate || "all time",
      },
      message: "Expensive items data retrieved successfully",
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logWithTimestamp("ERROR", "Failed to retrieve expensive items", {
      error: errorMessage,
    });

    return c.json(
      {
        success: false,
        error: errorMessage,
      },
      { status: 500 }
    );
  }
});

// Log that the app is ready
logWithTimestamp(
  "INFO",
  "✅ Server setup complete - ready to handle requests",
  {
    endpoints: [
      "GET /",
      "GET /hello",
      "GET /health",
      "GET /test-logs",
      "POST /handle-ticket-forwarding",
      "GET /receipts",
      "GET /receipts/:id",
      "DELETE /receipts/:id",
      "GET /analytics/monthly-spending",
      "GET /analytics/item-prices",
      "GET /analytics/store-spending",
      "GET /analytics/expensive-items",
    ],
  }
);

export default app;
