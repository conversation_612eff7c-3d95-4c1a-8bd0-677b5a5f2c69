import { describe, test, expect, beforeAll, afterAll } from "bun:test";
import { createClient } from "@supabase/supabase-js";
import {
  getServiceRoleSupabase,
  serviceRoleOperations,
} from "../lib/supabaseServiceRole";
import { UserService } from "../services/userService";
import type { Database } from "@shared/types/database";
import { getTestConfig } from "./test-config";

describe("RLS and Service Role Tests", () => {
  let anonClient: ReturnType<typeof createClient<Database>>;
  let serviceRoleClient: ReturnType<typeof getServiceRoleSupabase>;
  let testUserId: string;
  let testEmailAlias: string;

  beforeAll(async () => {
    // Get test configuration securely
    const config = getTestConfig();

    // Initialize clients
    anonClient = createClient<Database>(
      config.supabaseUrl,
      config.supabaseAnonKey
    );
    serviceRoleClient = getServiceRoleSupabase();

    // Create a test user in auth.users using service role
    const { data: authUser, error: authError } =
      await serviceRoleClient.auth.admin.createUser({
        email: "<EMAIL>",
        password: "testpassword123",
        email_confirm: true,
      });

    if (authError || !authUser.user) {
      throw new Error(`Failed to create test user: ${authError?.message}`);
    }

    testUserId = authUser.user.id;

    // Create user with alias using service role
    testEmailAlias = "test-beaver-123";
    await serviceRoleOperations.createUserWithAlias(testUserId, testEmailAlias);
  });

  afterAll(async () => {
    // Clean up test user
    if (testUserId) {
      await serviceRoleClient.auth.admin.deleteUser(testUserId);
    }
  });

  test("Service role can read users table (bypasses RLS)", async () => {
    const user =
      await serviceRoleOperations.getUserByEmailAlias(testEmailAlias);
    expect(user).toBeTruthy();
    expect(user?.email_alias).toBe(testEmailAlias);
    expect(user?.user_id).toBe(testUserId);
  });

  test("Anonymous client cannot read users table (RLS blocks)", async () => {
    const { data, error } = await anonClient
      .from("users")
      .select("*")
      .eq("email_alias", testEmailAlias);

    // Should return empty array due to RLS, not an error
    expect(data).toEqual([]);
  });

  test("Service role can create receipts for any user", async () => {
    const receipt = await serviceRoleOperations.insertReceiptForUser(
      testUserId,
      {
        store_name: "Test Store",
        purchase_date: "2024-01-01",
        purchase_total: 10.99,
      }
    );

    expect(receipt).toBeTruthy();
    expect(receipt.user_id).toBe(testUserId);
    expect(receipt.store_name).toBe("Test Store");
  });

  test("Anonymous client cannot read receipts without authentication", async () => {
    const { data, error } = await anonClient
      .from("receipts")
      .select("*")
      .eq("user_id", testUserId);

    // Should return empty array due to RLS
    expect(data).toEqual([]);
  });

  test("Service role can read all receipts (bypasses RLS)", async () => {
    const { data, error } = await serviceRoleClient
      .from("receipts")
      .select("*")
      .eq("user_id", testUserId);

    expect(error).toBeNull();
    expect(data).toBeTruthy();
    expect(data!.length).toBeGreaterThan(0);
    expect(data![0].user_id).toBe(testUserId);
  });

  test("Email alias generation creates unique aliases", async () => {
    const userService = new UserService();

    // Generate multiple aliases and check they're unique
    const aliases = await Promise.all([
      userService.generateUniqueAlias(),
      userService.generateUniqueAlias(),
      userService.generateUniqueAlias(),
    ]);

    // All should be different
    expect(new Set(aliases).size).toBe(3);

    // All should match the pattern adjective-animal-number
    aliases.forEach((alias) => {
      expect(alias).toMatch(/^[a-z]+-[a-z]+-\d+$/);
    });
  });

  test("User lookup by email alias works correctly", async () => {
    const userService = new UserService();

    // Should find the test user
    const foundUser = await userService.getUserByEmailAlias(testEmailAlias);
    expect(foundUser).toBeTruthy();
    expect(foundUser?.user_id).toBe(testUserId);
    expect(foundUser?.email_alias).toBe(testEmailAlias);

    // Should return null for non-existent alias
    const notFoundUser =
      await userService.getUserByEmailAlias("non-existent-alias");
    expect(notFoundUser).toBeNull();
  });

  test("Email alias availability check works", async () => {
    // Existing alias should not be available
    const isExistingAvailable =
      await serviceRoleOperations.isEmailAliasAvailable(testEmailAlias);
    expect(isExistingAvailable).toBe(false);

    // Non-existent alias should be available
    const isNewAvailable = await serviceRoleOperations.isEmailAliasAvailable(
      "definitely-unique-alias-999"
    );
    expect(isNewAvailable).toBe(true);
  });
});
