# Test Configuration

This directory contains tests for the RLS (Row Level Security) implementation and email forwarding functionality.

## Security Approach

### Environment Variables in Tests

The tests use environment variables from `.env.local` (for local development) or system environment variables (for production/CI).

- **Local Development**: Uses keys from `.env.local` (not committed to git)
- **Production/CI**: Uses system environment variables
- **No Hardcoded Keys**: All sensitive values come from environment variables

### Production Security

For production environments:

- Always use proper environment variables
- Never commit actual production keys to version control
- Use secrets management systems (AWS Secrets Manager, etc.)
- Rotate keys regularly

## Test Files

- `rls.test.ts` - Tests Row Level Security policies and service role bypass
- `email-forwarding.test.ts` - Tests email forwarding integration

## Running Tests

```bash
# Run all tests (loads .env.local automatically)
bun run test

# Run specific test file with env vars
bun test src/tests/rls.test.ts --env-file=.env.local

# Run with custom environment variables
SUPABASE_URL=your-url SUPABASE_SERVICE_ROLE_KEY=your-key bun test
```

## Test Environment Setup

1. Ensure Supabase is running locally: `supabase start`
2. Run database migrations: `supabase db reset`
3. Run tests: `bun test`

The tests will automatically:

- Set up test users and data
- Verify RLS policies work correctly
- Test service role bypass functionality
- Clean up test data after completion
