# Test Configuration

This directory contains tests for the RLS (Row Level Security) implementation and email forwarding functionality.

## Security Approach

### Environment Variables in Tests

The tests use a secure approach to handle environment variables:

1. **Primary**: Load from environment variables (`.env.local`, system env, etc.)
2. **Fallback**: Use default Supabase local development keys only if no env vars are set

### Why Default Keys Are Safe

The default Supabase keys included in `test-config.ts` are safe to commit because:

- **Local Only**: They only work with local Supabase instances (`127.0.0.1:54321`)
- **Public Documentation**: These are the standard keys documented in Supabase's official docs
- **No Production Access**: They cannot access any production data or services
- **Fallback Only**: They're only used when proper environment variables aren't set

### Production Security

For production environments:

- Always use proper environment variables
- Never commit actual production keys to version control
- Use secrets management systems (AWS Secrets Manager, etc.)
- Rotate keys regularly

## Test Files

- `rls.test.ts` - Tests Row Level Security policies and service role bypass
- `email-forwarding.test.ts` - Tests email forwarding integration
- `test-config.ts` - Secure test configuration utilities

## Running Tests

```bash
# Run all tests
bun test

# Run specific test file
bun test src/tests/rls.test.ts

# Run with environment variables
SUPABASE_URL=your-url SUPABASE_SERVICE_ROLE_KEY=your-key bun test
```

## Test Environment Setup

1. Ensure Supabase is running locally: `supabase start`
2. Run database migrations: `supabase db reset`
3. Run tests: `bun test`

The tests will automatically:
- Set up test users and data
- Verify RLS policies work correctly
- Test service role bypass functionality
- Clean up test data after completion
