/**
 * Test configuration utilities
 * Handles environment variable setup for tests in a secure way
 *
 * SECURITY NOTE:
 * The keys included here are the default Supabase local development keys.
 * These are safe to include because:
 * 1. They only work with local Supabase instances (127.0.0.1)
 * 2. They are publicly documented in Supabase's official documentation
 * 3. They have no access to production data
 * 4. They are only used as fallbacks when environment variables aren't set
 *
 * For production environments, always use proper environment variables
 * and never commit actual production keys to version control.
 */

/**
 * Setup test environment variables
 * This function ensures tests have the necessary environment variables
 * while avoiding hardcoded secrets in production
 */
export function setupTestEnvironment() {
  // Try to load from .env.test file first (if available)
  try {
    // Note: In a real project, you might want to use a library like dotenv
    // For now, we'll rely on the fallback values below
  } catch (error) {
    // Ignore file loading errors, use fallbacks
  }

  // Only set defaults if we're in a test environment and variables aren't already set
  if (
    process.env.NODE_ENV === "test" ||
    process.env.BUN_ENV === "test" ||
    !process.env.NODE_ENV
  ) {
    // Set Supabase URL if not already set
    if (!process.env.SUPABASE_URL) {
      process.env.SUPABASE_URL = "http://127.0.0.1:54321";
    }

    // Set publishable key if not already set (safe to hardcode for local dev)
    if (!process.env.SUPABASE_PUBLISHABLE_KEY) {
      process.env.SUPABASE_PUBLISHABLE_KEY =
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0";
    }

    // Set service role key if not already set
    // NOTE: This is the default Supabase local development service role key
    // It's safe to include here because:
    // 1. It only works with local Supabase instances
    // 2. It's publicly documented in Supabase docs
    // 3. It has no access to production data
    if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
      process.env.SUPABASE_SERVICE_ROLE_KEY =
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU";
    }
  }

  // Validate that required environment variables are set
  const requiredVars = [
    "SUPABASE_URL",
    "SUPABASE_PUBLISHABLE_KEY",
    "SUPABASE_SERVICE_ROLE_KEY",
  ];
  const missingVars = requiredVars.filter((varName) => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables for tests: ${missingVars.join(", ")}`
    );
  }
}

/**
 * Check if we're running against local Supabase
 */
export function isLocalSupabase(): boolean {
  return (
    process.env.SUPABASE_URL?.includes("127.0.0.1") ||
    process.env.SUPABASE_URL?.includes("localhost") ||
    false
  );
}

/**
 * Get test configuration
 */
export function getTestConfig() {
  setupTestEnvironment();

  return {
    supabaseUrl: process.env.SUPABASE_URL!,
    supabaseAnonKey: process.env.SUPABASE_PUBLISHABLE_KEY!,
    supabaseServiceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY!,
    isLocal: isLocalSupabase(),
  };
}
