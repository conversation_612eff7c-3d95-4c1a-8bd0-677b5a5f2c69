import { describe, test, expect, beforeAll, afterAll } from "bun:test";
import { serviceRoleOperations } from "../lib/supabaseServiceRole";

describe("Email Forwarding Integration Tests", () => {
  let testUserId: string;
  let testEmailAlias: string;

  beforeAll(async () => {
    // Set environment variables
    process.env.SUPABASE_URL = "http://127.0.0.1:54321";
    process.env.SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU";

    // Create a test user for email forwarding
    const { getServiceRoleSupabase } = await import("../lib/supabaseServiceRole");
    const serviceRoleClient = getServiceRoleSupabase();

    const { data: authUser, error: authError } = await serviceRoleClient.auth.admin.createUser({
      email: "<EMAIL>",
      password: "testpassword123",
      email_confirm: true,
    });

    if (authError || !authUser.user) {
      throw new Error(`Failed to create test user: ${authError?.message}`);
    }

    testUserId = authUser.user.id;
    testEmailAlias = "happy-beaver-999";
    
    // Create user with specific alias
    await serviceRoleOperations.createUserWithAlias(testUserId, testEmailAlias);
  });

  afterAll(async () => {
    // Clean up test user
    if (testUserId) {
      const { getServiceRoleSupabase } = await import("../lib/supabaseServiceRole");
      const serviceRoleClient = getServiceRoleSupabase();
      await serviceRoleClient.auth.admin.deleteUser(testUserId);
    }
  });

  test("Email forwarding endpoint can lookup user and process receipt", async () => {
    // Simulate a Mailgun webhook payload
    const mockEmailPayload = {
      recipient: `${testEmailAlias}@yourdomain.com`,
      sender: "<EMAIL>",
      subject: "Your Receipt",
      "body-plain": "Thank you for your purchase!",
      "attachment-count": "1",
      "attachment-1": {
        filename: "receipt.pdf",
        // This would normally be a PDF buffer, but for testing we'll mock the parsing
      }
    };

    // Test the user lookup part of the flow
    const foundUser = await serviceRoleOperations.getUserByEmailAlias(testEmailAlias);
    expect(foundUser).toBeTruthy();
    expect(foundUser?.user_id).toBe(testUserId);
    expect(foundUser?.email_alias).toBe(testEmailAlias);

    // Test receipt creation for the user
    const testReceipt = await serviceRoleOperations.insertReceiptForUser(testUserId, {
      store_name: "Test Store",
      purchase_date: "2024-01-01",
      purchase_total: 25.99,
      raw_pdf_text: "Mock PDF content for testing",
    });

    expect(testReceipt).toBeTruthy();
    expect(testReceipt.user_id).toBe(testUserId);
    expect(testReceipt.store_name).toBe("Test Store");
    expect(testReceipt.purchase_total).toBe(25.99);
  });

  test("Email forwarding handles unknown email aliases gracefully", async () => {
    const unknownAlias = "unknown-alias-123";
    
    const foundUser = await serviceRoleOperations.getUserByEmailAlias(unknownAlias);
    expect(foundUser).toBeNull();
  });

  test("Service role can create receipts with items", async () => {
    const receipt = await serviceRoleOperations.insertReceiptForUser(testUserId, {
      store_name: "Grocery Store",
      purchase_date: "2024-01-15",
      purchase_total: 45.67,
    });

    const items = await serviceRoleOperations.insertReceiptItems([
      {
        receipt_id: receipt.id,
        item_name: "Apples",
        item_price: 3.99,
        item_quantity: 1,
        item_total_price: 3.99,
        line_order: 1,
      },
      {
        receipt_id: receipt.id,
        item_name: "Bread",
        item_price: 2.50,
        item_quantity: 2,
        item_total_price: 5.00,
        line_order: 2,
      }
    ]);

    expect(items).toBeTruthy();
    expect(items.length).toBe(2);
    expect(items[0].item_name).toBe("Apples");
    expect(items[1].item_name).toBe("Bread");
  });
});
