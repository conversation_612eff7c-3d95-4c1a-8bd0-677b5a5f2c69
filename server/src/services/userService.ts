import type { Context } from "hono";
import { getSupabase } from "../lib/middleware/supabase";
import { serviceRoleOperations } from "../lib/supabaseServiceRole";
import type { Database } from "@shared/types/database";

type User = Database["public"]["Tables"]["users"]["Row"];

/**
 * Word lists for generating random email aliases
 */
const ADJECTIVES = [
  "happy",
  "clever",
  "bright",
  "swift",
  "gentle",
  "brave",
  "calm",
  "wise",
  "kind",
  "bold",
  "quick",
  "smart",
  "cool",
  "warm",
  "fresh",
  "neat",
  "clean",
  "clear",
  "sharp",
  "smooth",
  "strong",
  "light",
  "dark",
  "soft",
  "loud",
  "quiet",
  "fast",
  "slow",
  "big",
  "small",
  "tall",
  "short",
  "wide",
  "thin",
  "thick",
  "deep",
  "high",
  "low",
  "new",
  "old",
  "young",
  "rich",
  "poor",
  "full",
  "empty",
  "busy",
  "free",
  "easy",
  "hard",
  "good",
  "bad",
  "best",
  "worst",
  "first",
  "last",
  "early",
  "late",
  "hot",
  "cold",
  "wet",
  "dry",
  "open",
  "closed",
  "safe",
  "dangerous",
  "healthy",
  "sick",
  "hungry",
  "thirsty",
  "tired",
  "awake",
];

const ANIMALS = [
  "beaver",
  "rabbit",
  "squirrel",
  "fox",
  "deer",
  "bear",
  "wolf",
  "owl",
  "eagle",
  "hawk",
  "robin",
  "sparrow",
  "dove",
  "swan",
  "duck",
  "goose",
  "penguin",
  "seal",
  "whale",
  "dolphin",
  "shark",
  "fish",
  "crab",
  "lobster",
  "turtle",
  "frog",
  "snake",
  "lizard",
  "cat",
  "dog",
  "horse",
  "cow",
  "pig",
  "sheep",
  "goat",
  "chicken",
  "rooster",
  "turkey",
  "mouse",
  "rat",
  "hamster",
  "guinea",
  "ferret",
  "hedgehog",
  "raccoon",
  "skunk",
  "opossum",
  "chipmunk",
  "mole",
  "bat",
  "monkey",
  "ape",
  "gorilla",
  "lion",
  "tiger",
  "leopard",
  "cheetah",
  "elephant",
  "rhino",
  "hippo",
  "giraffe",
  "zebra",
  "kangaroo",
  "koala",
  "panda",
  "sloth",
  "armadillo",
  "anteater",
];

export class UserService {
  /**
   * Generate a random email alias in the format "adjective-animal-number"
   */
  private generateRandomAlias(): string {
    const adjective = ADJECTIVES[Math.floor(Math.random() * ADJECTIVES.length)];
    const animal = ANIMALS[Math.floor(Math.random() * ANIMALS.length)];
    const number = Math.floor(Math.random() * 1000) + 1; // 1-1000

    return `${adjective}-${animal}-${number}`;
  }

  /**
   * Generate a unique email alias by checking availability
   */
  async generateUniqueAlias(maxAttempts = 10): Promise<string> {
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      const alias = this.generateRandomAlias();
      const isAvailable =
        await serviceRoleOperations.isEmailAliasAvailable(alias);

      if (isAvailable) {
        return alias;
      }
    }

    // Fallback to timestamp-based alias if we can't find a unique one
    const timestamp = Date.now();
    const adjective = ADJECTIVES[Math.floor(Math.random() * ADJECTIVES.length)];
    const animal = ANIMALS[Math.floor(Math.random() * ANIMALS.length)];
    return `${adjective}-${animal}-${timestamp}`;
  }

  /**
   * Create a new user with a unique email alias
   * This uses the regular Supabase client (with RLS) for user-facing operations
   */
  async createUser(c: Context, userId: string): Promise<User> {
    try {
      // Generate unique alias
      const emailAlias = await this.generateUniqueAlias();

      // Use regular supabase client for user creation (respects RLS)
      const supabase = getSupabase(c);
      const { data, error } = await supabase
        .from("users")
        .insert({
          user_id: userId,
          email_alias: emailAlias,
        })
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to create user: ${error.message}`);
      }

      return data as User;
    } catch (error) {
      throw new Error(
        `User creation failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Get user by user_id (authenticated user context)
   */
  async getUserByUserId(c: Context, userId: string): Promise<User | null> {
    try {
      const supabase = getSupabase(c);
      const { data, error } = await supabase
        .from("users")
        .select("*")
        .eq("user_id", userId)
        .single();

      if (error && error.code !== "PGRST116") {
        throw new Error(`Failed to get user: ${error.message}`);
      }

      return data as User | null;
    } catch (error) {
      throw new Error(
        `Failed to get user: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Get user by email alias (server-side operation, bypasses RLS)
   * Used by /handle-ticket-forwarding endpoint
   */
  async getUserByEmailAlias(emailAlias: string): Promise<User | null> {
    try {
      return await serviceRoleOperations.getUserByEmailAlias(emailAlias);
    } catch (error) {
      throw new Error(
        `Failed to get user by email alias: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Update user's email alias
   */
  async updateEmailAlias(
    c: Context,
    userId: string,
    newAlias: string
  ): Promise<User> {
    try {
      // Check if new alias is available
      const isAvailable =
        await serviceRoleOperations.isEmailAliasAvailable(newAlias);
      if (!isAvailable) {
        throw new Error("Email alias is already taken");
      }

      const supabase = getSupabase(c);
      const { data, error } = await supabase
        .from("users")
        .update({ email_alias: newAlias })
        .eq("user_id", userId)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update email alias: ${error.message}`);
      }

      return data as User;
    } catch (error) {
      throw new Error(
        `Email alias update failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Delete user
   */
  async deleteUser(c: Context, userId: string): Promise<boolean> {
    try {
      const supabase = getSupabase(c);
      const { error } = await supabase
        .from("users")
        .delete()
        .eq("user_id", userId);

      if (error) {
        throw new Error(`Failed to delete user: ${error.message}`);
      }

      return true;
    } catch (error) {
      throw new Error(
        `User deletion failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }
}
