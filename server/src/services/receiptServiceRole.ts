import { serviceRoleOperations } from "../lib/supabaseServiceRole";
import type { Database } from "@shared/types/database";
import type {
  Receipt,
  ReceiptItem,
  CreateReceiptInput,
  CreateReceiptItemInput,
} from "shared";

/**
 * Service role version of ReceiptService that bypasses RLS
 * Used for server-side operations like email forwarding
 */
export class ReceiptServiceRole {
  /**
   * Create a new receipt with its items using service role (bypasses RLS)
   * Used by /handle-ticket-forwarding to create receipts for specific users
   */
  async createReceiptWithItemsForUser(
    userId: string,
    receiptData: Omit<CreateReceiptInput, "user_id">,
    items: Omit<CreateReceiptItemInput, "receipt_id">[]
  ): Promise<{ receipt: Receipt; items: ReceiptItem[] }> {
    try {
      // Insert receipt with specific user_id using service role
      const receipt = await serviceRoleOperations.insertReceiptForUser(userId, {
        store_name: receiptData.store_name,
        store_address: receiptData.store_address || null,
        store_city: receiptData.store_city || null,
        store_state: receiptData.store_state || null,
        store_zip_code: receiptData.store_zip_code || null,
        store_phone_number: receiptData.store_phone_number || null,
        purchase_date: receiptData.purchase_date,
        purchase_time: receiptData.purchase_time || null,
        ticket_number: receiptData.ticket_number || null,
        purchase_total: receiptData.purchase_total,
        purchase_tax: receiptData.purchase_tax || null,
        credit_card_last_4: receiptData.credit_card_last_4 || null,
        payment_method: receiptData.payment_method || null,
        raw_pdf_text: receiptData.raw_pdf_text || null,
      });

      if (!receipt) {
        throw new Error("Receipt was not created");
      }

      // Create receipt items
      const itemsToInsert = items.map((item, index) => ({
        receipt_id: receipt.id,
        item_name: item.item_name,
        item_price: item.item_price,
        item_quantity: item.item_quantity,
        item_total_price: item.item_total_price,
        line_order: item.line_order || index + 1,
      }));

      const createdItems = await serviceRoleOperations.insertReceiptItems(itemsToInsert);

      if (!createdItems) {
        throw new Error("Receipt items were not created");
      }

      // Create master items and matches for MVP (1:1 relationship)
      await this.createMasterItemsForReceiptItems(createdItems);

      return {
        receipt: receipt as Receipt,
        items: createdItems as ReceiptItem[],
      };
    } catch (error) {
      throw new Error(
        `Receipt creation failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Create master items and matches for receipt items (MVP approach)
   * Uses service role to bypass RLS
   */
  private async createMasterItemsForReceiptItems(
    receiptItems: ReceiptItem[]
  ): Promise<void> {
    const { getServiceRoleSupabase } = await import("../lib/supabaseServiceRole");
    const supabase = getServiceRoleSupabase();

    for (const item of receiptItems) {
      try {
        // Check if master item already exists
        const { data: existingMasterItem, error: searchError } = await supabase
          .from("master_items")
          .select("id")
          .eq("normalized_name", item.item_name.toLowerCase().trim())
          .single();

        if (searchError && searchError.code !== "PGRST116") {
          throw new Error(`Failed to search master item: ${searchError.message}`);
        }

        let masterItemId: string;

        if (existingMasterItem) {
          // Use existing master item
          masterItemId = existingMasterItem.id;
        } else {
          // Create new master item
          const { data: newMasterItem, error: insertError } = await supabase
            .from("master_items")
            .insert({
              normalized_name: item.item_name.toLowerCase().trim(),
              category: null, // Will be categorized later
            })
            .select()
            .single();

          if (insertError) {
            throw new Error(`Failed to create master item: ${insertError.message}`);
          }

          masterItemId = newMasterItem.id;
        }

        // Create item match
        const { error: matchError } = await supabase
          .from("item_matches")
          .insert({
            receipt_item_id: item.id,
            master_item_id: masterItemId,
            confidence_score: 1.0,
            match_method: "exact",
          });

        if (matchError) {
          throw new Error(`Failed to create item match: ${matchError.message}`);
        }
      } catch (error) {
        // Log error but don't fail the entire operation
        console.error(`Failed to create master item for ${item.item_name}:`, error);
      }
    }
  }
}
