import { createClient } from "@supabase/supabase-js";
import { createServerClient } from "@supabase/ssr";
import { SupabaseClient } from "@supabase/supabase-js";
import type { Context, MiddlewareHandler } from "hono";
import { env } from "hono/adapter";
import type { Database } from "@shared/types/database";

declare module "hono" {
  interface ContextVariableMap {
    supabaseServiceRole: SupabaseClient<Database>;
  }
}

// Singleton service role client for non-Hono contexts (like tests)
let serviceRoleClient: SupabaseClient<Database> | null = null;

/**
 * Get service role client from Hono context (preferred for route handlers)
 */
export const getServiceRoleSupabase = (
  c: Context
): SupabaseClient<Database> => {
  return c.get("supabaseServiceRole");
};

/**
 * Get service role client directly (for tests and non-Hono contexts)
 */
export const getServiceRoleSupabaseDirect = (): SupabaseClient<Database> => {
  if (serviceRoleClient) {
    return serviceRoleClient;
  }

  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl) {
    throw new Error("SUPABASE_URL environment variable is required");
  }

  if (!supabaseServiceRoleKey) {
    throw new Error(
      "SUPABASE_SERVICE_ROLE_KEY environment variable is required"
    );
  }

  serviceRoleClient = createClient<Database>(
    supabaseUrl,
    supabaseServiceRoleKey,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    }
  );

  return serviceRoleClient;
};

type ServiceRoleEnv = {
  SUPABASE_URL: string;
  SUPABASE_SERVICE_ROLE_KEY: string;
};

export const supabaseServiceRoleMiddleware = (): MiddlewareHandler => {
  return async (c, next) => {
    const supabaseEnv = env<ServiceRoleEnv>(c);
    const supabaseUrl = supabaseEnv.SUPABASE_URL;
    const supabaseServiceRoleKey = supabaseEnv.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl) {
      throw new Error("SUPABASE_URL missing!");
    }

    if (!supabaseServiceRoleKey) {
      throw new Error("SUPABASE_SERVICE_ROLE_KEY missing!");
    }

    const supabase = createServerClient<Database>(
      supabaseUrl,
      supabaseServiceRoleKey,
      {
        cookies: {
          getAll() {
            // Service role doesn't need cookies, but we need to provide the interface
            return [];
          },
          setAll() {
            // Service role doesn't set cookies
          },
        },
      }
    );

    c.set("supabaseServiceRole", supabase);

    await next();
  };
};

/**
 * Service role operations that bypass RLS
 * These should only be used for server-side operations like:
 * - Email forwarding lookups
 * - Admin operations
 * - System-level data processing
 */
export const serviceRoleOperations = {
  /**
   * Find user by email alias
   * Used by /handle-ticket-forwarding to lookup which user should receive the receipt
   */
  async getUserByEmailAlias(emailAlias: string) {
    const supabase = getServiceRoleSupabaseDirect();
    const { data, error } = await supabase
      .from("users")
      .select("*")
      .eq("email_alias", emailAlias)
      .single();

    if (error && error.code !== "PGRST116") {
      throw new Error(`Failed to find user by email alias: ${error.message}`);
    }

    return data; // null if not found
  },

  /**
   * Create a new user with a unique email alias
   */
  async createUserWithAlias(userId: string, emailAlias: string) {
    const supabase = getServiceRoleSupabaseDirect();
    const { data, error } = await supabase
      .from("users")
      .insert({
        user_id: userId,
        email_alias: emailAlias,
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create user with alias: ${error.message}`);
    }

    return data;
  },

  /**
   * Insert receipt with specific user_id (bypasses RLS)
   * Used by /handle-ticket-forwarding to create receipts for users
   */
  async insertReceiptForUser(
    userId: string,
    receipt: Database["public"]["Tables"]["receipts"]["Insert"]
  ) {
    const supabase = getServiceRoleSupabaseDirect();
    const { data, error } = await supabase
      .from("receipts")
      .insert({
        ...receipt,
        user_id: userId,
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to insert receipt for user: ${error.message}`);
    }

    return data;
  },

  /**
   * Insert receipt items (bypasses RLS)
   */
  async insertReceiptItems(
    items: Database["public"]["Tables"]["receipt_items"]["Insert"][]
  ) {
    const supabase = getServiceRoleSupabaseDirect();
    const { data, error } = await supabase
      .from("receipt_items")
      .insert(items)
      .select();

    if (error) {
      throw new Error(`Failed to insert receipt items: ${error.message}`);
    }

    return data;
  },

  /**
   * Check if email alias is available
   */
  async isEmailAliasAvailable(emailAlias: string): Promise<boolean> {
    const supabase = getServiceRoleSupabaseDirect();
    const { data, error } = await supabase
      .from("users")
      .select("id")
      .eq("email_alias", emailAlias)
      .single();

    if (error && error.code === "PGRST116") {
      return true; // Not found, so available
    }

    if (error) {
      throw new Error(
        `Failed to check email alias availability: ${error.message}`
      );
    }

    return false; // Found, so not available
  },
};
