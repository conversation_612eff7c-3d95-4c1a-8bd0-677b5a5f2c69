{"name": "server", "version": "0.0.1", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "bun --watch run src/index.ts", "test": "bun test --env-file=.env.local", "test:watch": "bun test --watch --env-file=.env.local", "gen:types": "supabase gen types --lang=typescript --local> ../shared/src/types/database.ts"}, "dependencies": {"@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.55.0", "@types/multer": "^2.0.0", "hono": "^4.7.11", "multer": "^2.0.2", "pdf-extraction": "^1.0.2", "shared": "workspace:*"}, "devDependencies": {"@types/bun": "latest"}}