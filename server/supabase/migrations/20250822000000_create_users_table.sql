-- Create users table for email alias management
CREATE TABLE users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    email_alias TEXT NOT NULL UNIQUE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one user record per auth user
    UNIQUE(user_id)
);

-- Create indexes for efficient querying
CREATE INDEX idx_users_user_id ON users(user_id);
CREATE INDEX idx_users_email_alias ON users(email_alias);

-- Add updated_at trigger
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- RLS Policies for users table
-- Users can view their own user record
CREATE POLICY "Users can view their own user record" ON users
    FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own user record
CREATE POLICY "Users can insert their own user record" ON users
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own user record
CREATE POLICY "Users can update their own user record" ON users
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own user record
CREATE POLICY "Users can delete their own user record" ON users
    FOR DELETE USING (auth.uid() = user_id);

-- Service role can access all user records (bypasses RLS automatically)
-- No additional policies needed for service role as it bypasses RLS entirely

-- Update existing receipts table to add foreign key constraint to users table
-- Note: This is optional since receipts.user_id already references auth.users(id)
-- and users.user_id also references auth.users(id), creating an indirect relationship

-- Add comment for documentation
COMMENT ON TABLE users IS 'User profiles with email aliases for receipt forwarding';
COMMENT ON COLUMN users.user_id IS 'References auth.users(id) - the authenticated user';
COMMENT ON COLUMN users.email_alias IS 'Unique email alias for receipt forwarding (e.g., "happy-beaver-123")';
